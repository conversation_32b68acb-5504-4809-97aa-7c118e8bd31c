# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
# Data files (keep structure, ignore content)
data/*/
!data/.gitkeep
src/data/*/
!src/data/.gitkeep

# Charts output
charts/
src/charts/

# Test outputs
test_charts/

# Logs
*.log
logs/

# Environment variables (keep template)
.env.local
.env.production

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Backup files
*.bak
*.backup
*~

# Local configuration
config.local.json
local_settings.py
