# Stav vývoje

## Aktu<PERSON>lní verze: 0.1.0

### Probíhající práce

- Implementace autentizace API (limesurvey_client, config_loader)  
- Revize dokumentace (md soubory)  
- Refaktorizace kódu a ošetření chyb při spuštění  
  - Lazy import python-dotenv a pandas  
  - Ošetření EOFError v CLI menu  
- Rozšíření jednotkových testů a jejich úspěšné spuštění  

### Dokončené úkoly

- Inicializace Git repozitáře  
- Vytvoření základní struktury projektu  
- Implementace základní třídy pro komunikaci s API  
- Ošetření načítání .env souboru v absence `python-dotenv`  
- Ošetření importu pandas v absence `pandas`  
- Ošetření EOFError v CLI menu  
- Konfigurace CI/CD pipeline  
- Základní implementace GUI modulů (PyQt5)  

### Plánovan<PERSON> úkoly - Fáze 1 (Konsolidace)

**Okamžité (leden 2025):**
- [ ] Aktualizace dokumentace (README, user guide)
- [ ] Stabilizace CLI menu a oprava všech známých bugů
- [ ] Rozšíření unit testů na 80% pokrytí
- [ ] Refaktoring konfiguračního systému pro rozšiřitelnost

**Krátkodobé (únor-březen 2025):**
- [ ] Přidání cachování (response cache, Datawrapper cache)
- [ ] Filtrování průzkumů podle parametrů (datum, počet odpovědí)
- [ ] Implementace anonymizace citlivých dat a logování chyb
- [ ] Dokončení mapování struktury (manuální GUI)

### Plánované úkoly - Fáze 2+ (Q2 2025+)

**Infrastruktura:**
- [ ] Podpora více LimeSurvey serverů
- [ ] Rozšířená metadata a konfigurace
- [ ] Optimalizace výkonu

**Rozšíření funkcí:**
- [ ] Nové typy grafů (tabulky, koláče, skupinové)
- [ ] AI funkce (překlad, komentáře, analýzy)
- [ ] GUI rozšíření s AI chatbotem
- [ ] Speciální analýzy a WordCloud grafy

Detailní plán viz [06_plan_rozsireni.md](06_plan_rozsireni.md)

## Detailní popis aktuálního úkolu

### Refaktorizace a zajištění spustitelnosti aplikace

**Cíl:**

Zajistit, aby aplikace nepadala při chybějících závislostech a podporovala piped input, a aktualizovat stávající kód pro stabilní běh.

**Kroky implementace:**

1. Přidat lazy import pro `python-dotenv` a `pandas` s fallbackem.  
2. Odebrat anotace odkazující na `pd.DataFrame` pro kompatibilitu bez pandas.  
3. Ošetřit `EOFError` při čtení vstupu (`input()`) v src/main.py a funkcích CLI.  
4. Aktualizovat jednotkové testy (`test_transformer.py`, `test_cli.py` atd.) pro nové chování.  
5. Spustit CI pipeline a zajistit úspěšné spuštění všech testů.  

**Relevantní soubory:**

- src/main.py  
- src/data_transformer.py  
- src/limesurvey_client.py  
- src/config_loader.py  
- src/cli.py  
- test_transformer.py  
- test_cli.py  

**Testovací scénáře:**

- Spuštění CLI menu interaktivně i s piped input.  
- Volání funkcí bez nainstalovaného `python-dotenv` a `pandas`.  
- Ověření, že testy proběhnou bez chyb (`pytest`).  

**Příkazy pro aider:**

```
/edit src/data_transformer.py src/main.py src/limesurvey_client.py src/config_loader.py
/run pytest -q
