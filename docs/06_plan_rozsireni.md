# P<PERSON>án rozšíření aplikace LimeSurvey-Datawrapper

## P<PERSON><PERSON>led navrhovaných rozšíření

Tento dokument obsahuje strukturovaný plán rozšíření aplikace na základě požadavků uživatele. Rozšíření jsou rozdělena do kategorií podle priority a složitosti implementace.

## Kategorie rozšíření

### A. Infrastruktura a konfigurace (Priorita: Vysoká)

#### A1. Podpora více LimeSurvey serverů
**Popis:** Implementace možnosti připojení k více LimeSurvey serverům současně
**Implementace:**
- Rozšíření .env konfigurace o více sad přihlašovacích údajů
- Výběr serveru při spuštění aplikace
- Úprava souborové struktury pro oddělení dat podle serverů
- Řešení koliz<PERSON> stejn<PERSON>ch ID průzkumů z různých serverů

**Dopad na strukturu:**
```
data/
├── server1_example.com/
│   ├── 827822/
│   └── 778769/
└── server2_test.com/
    ├── 827822/  # stejné ID, jiný server
    └── 945123/
```

**Fáze implementace:** Fáze 2 - po konsolidaci základní funkcionality

#### A2. Rozšířená metadata a konfigurace
**Popis:** Systém editovatelných metadat pro standardizaci grafů
**Implementace:**
- JSON struktura s defaultními hodnotami pro Layout, Annotate, Refine
- Kopírování do adresáře průzkumu pro projektové úpravy
- Jazykové mutace metadat
- Integrace do generování grafů

**Struktura metadat:**
```json
{
  "default_metadata": {
    "layout": {...},
    "annotate": {...},
    "refine": {...}
  },
  "language_specific": {
    "cs-CZ": {...},
    "en-US": {...}
  }
}
```

**Fáze implementace:** Fáze 2 - po dokončení základního systému

### B. Rozšíření typů grafů (Priorita: Střední)

#### B1. Výběr typu grafu
**Popis:** Možnost volby typu grafu pro každou otázku
**Implementace:**
- Čtení preferovaného typu z LSS struktury (pokud je uložen)
- Manuální výběr z dostupných typů pro daný typ otázky
- UI pro mapování otázka → typ grafu

**Podporované typy:**
- Koláčové grafy (pie, donut)
- Sloupcové (vertical, horizontal)
- Radiální grafy
- Tabulky

**Fáze implementace:** Fáze 3 - po stabilizaci základních grafů

#### B2. Implementace tabulkových grafů
**Popis:** Podpora pro zobrazení dat v tabulkové formě
**Implementace:**
- Nový typ grafu v Datawrapper API
- Speciální formátování pro tabulková data
- Možnost agregace a třídění

**Fáze implementace:** Fáze 3

#### B3. Vícenásobné a skupinové grafy
**Popis:** Spojování otázek do skupin pro společné zobrazení
**Implementace:**
- Grouped columns pro srovnání lokalit/skupin
- Multiple donuts pro stejné otázky z různých skupin
- Složené sloupcové grafy

**Příklad použití:** 6 stejných otázek pro různé lokality
**Fáze implementace:** Fáze 4 - pokročilé funkce

### C. AI a automatizace (Priorita: Střední až Nízká)

#### C1. AI překlad
**Popis:** Automatický překlad JSON struktur pomocí LLM
**Implementace:**
- Menu položka pro AI překlad
- Editovatelné prompty pro překlad
- Podpora různých jazyků
- Ochrana citlivých dat

**Fáze implementace:** Fáze 4 - po dokončení manuálního překladového systému

#### C2. AI komentáře grafů
**Popis:** Automatické generování komentářů pro patičky grafů
**Implementace:**
- Analýza dat jednotlivých grafů
- Editovatelné prompty pro hodnocení
- Integrace do Datawrapper metadat

**Fáze implementace:** Fáze 5 - pokročilé AI funkce

#### C3. AI zpracování textových otázek
**Popis:** Kategorizace a transformace volných textových odpovědí
**Implementace:**
- Uživatelsky definované prompty
- Předpřipravené sady funkcí (kategorizace, sentiment analýza)
- Workflow pro výběr otázek a operací

**Fáze implementace:** Fáze 5

#### C4. AI senior analytik
**Popis:** Analýza vazeb mezi otázkami a generování zpráv
**Implementace:**
- Korelační analýzy
- Automatické generování závěrů
- Statistické testy
- UI pro výběr analyzovaných dat

**Fáze implementace:** Fáze 6 - pokročilé analytické funkce

### D. Speciální grafy a analýzy (Priorita: Nízká)

#### D1. WordCloud grafy
**Popis:** Grafy typu wordcloud pro textová data
**Implementace:**
- LLM transformace textů na klíčová slova
- Vlastní generování PNG pomocí Python knihoven
- Parametry kompatibilní s Datawrapper

**Fáze implementace:** Fáze 5

#### D2. Analýzy mimo LSS strukturu
**Popis:** Grafy z CSV dat neodpovídajících otázkám
**Implementace:**
- Analýza kompletnosti vyplnění
- Časové analýzy (počty respondentů, doba vyplnění)
- Technické analýzy (IP adresy, referrery)
- Detekce robotů vs. lidí

**Fáze implementace:** Fáze 6

#### D3. Rozšíření typů otázek
**Popis:** Podpora všech typů LimeSurvey otázek
**Implementace:**
- Mapování každého typu otázky na vhodné typy grafů
- Speciální zpracování pro komplexní typy
- Dokumentace podporovaných kombinací

**Fáze implementace:** Průběžně podle potřeby

### E. Uživatelské rozhraní (Priorita: Střední)

#### E1. Optimalizace CLI workflow
**Popis:** Komplexnější vícekrokové zpracování
**Implementace:**
- Průvodce nastavením projektu
- Kontrolní body (metadata, překlady)
- Rozdělení na fáze: Nastavení → Zpracování → Analýza

**Fáze implementace:** Fáze 3

#### E2. GUI implementace
**Popis:** Grafické uživatelské rozhraní
**Implementace:**
- Rozšíření stávajícího PyQt5 GUI
- AI chatbot pro interakci s daty
- Drag & drop pro přenos dat mezi otázkami

**Fáze implementace:** Fáze 4-5

### F. Rozšířené funkce (Priorita: Nízká)

#### F1. Využití LimeSurvey metadat
**Popis:** Využití všech dostupných metadat z průzkumů
**Implementace:**
- Analýza dostupných metadat v LSS
- Integrace do analýz a textů
- Dokumentace využitelných dat

**Fáze implementace:** Fáze 6

#### F2. Tiskové verze průzkumů
**Popis:** Export průzkumu do tisknutelné formy
**Implementace:**
- Parsing LSS struktury
- Generování PDF/HTML bez JS/CSS závislostí
- Editovatelné šablony

**Fáze implementace:** Fáze 6 - specializovaná funkce

## Harmonogram implementace

### Fáze 1: Konsolidace (aktuálně probíhá)
- Dokončení základní funkcionality
- Stabilizace stávajícího kódu
- Testování a dokumentace

### Fáze 2: Infrastruktura (Q2 2025)
- A1: Podpora více serverů
- A2: Rozšířená metadata
- Optimalizace výkonu

### Fáze 3: Rozšíření grafů (Q3 2025)
- B1: Výběr typu grafu
- B2: Tabulkové grafy
- E1: Optimalizace CLI

### Fáze 4: Pokročilé funkce (Q4 2025)
- B3: Skupinové grafy
- C1: AI překlad
- E2: GUI rozšíření

### Fáze 5: AI funkce (Q1 2026)
- C2: AI komentáře
- C3: AI zpracování textů
- D1: WordCloud grafy

### Fáze 6: Specializované funkce (Q2 2026)
- C4: AI senior analytik
- D2: Speciální analýzy
- F1, F2: Rozšířené funkce

## Poznámky k implementaci

### Okamžité kroky (do konce ledna 2025)
1. Dokončit konsolidaci stávající funkcionality
2. Vytvořit detailní dokumentaci aktuálního stavu
3. Připravit roadmapu pro Fázi 2

### Rozhodnutí k diskusi
1. Prioritizace AI funkcí vs. rozšíření typů grafů
2. Rozsah GUI implementace
3. Integrace s dalšími nástroji (PowerBI, Tableau)

### Technické požadavky
- Zachování zpětné kompatibility
- Modulární architektura pro snadné rozšiřování
- Dokumentace API pro třetí strany
- Automatizované testování všech nových funkcí
