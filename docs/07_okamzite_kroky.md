# Okamžité kroky k implementaci

## Kroky k realizaci nyní (leden 2025)

Na základě praktických potřeb a zkušeností s opakovaným generováním dat:

### 1. Refaktoring datové struktury (2-3 dny)
**Priorita: KRITICKÁ**

#### Co udělat:
- [ ] Přesunout `src/data/` → `data/` (na root level)
- [ ] Přesunout `src/charts/` → `charts/` (na root level)
- [ ] Vytvořit vnořenou strukturu podle serverů:
```
data/
├── server1_dotazniky.urad.online/
│   ├── 827822/
│   └── 778769/
└── server2_nazev/
    └── survey_id/
```
- [ ] Aktualizovat všechny cesty v kódu
- [ ] Otestovat na stávajících datech

#### Důvod:
Nutné pro podporu více serverů a lepší organizaci dat.

### 2. Podpora více LimeSurvey serverů (3-4 dny)
**Priorita: KRITICKÁ**

#### Co udělat:
- [ ] Rozšířit .env o více serverů:
```
# Server 1
LIMESURVEY_SERVER1_NAME=dotazniky.urad.online
LIMESURVEY_SERVER1_URL=https://dotazniky.urad.online/index.php?r=admin/remotecontrol
LIMESURVEY_SERVER1_USERNAME=abc
LIMESURVEY_SERVER1_PASSWORD=kgfaRpK8in9kPQK

# Server 2
LIMESURVEY_SERVER2_NAME=server2_nazev
LIMESURVEY_SERVER2_URL=...
LIMESURVEY_SERVER2_USERNAME=...
LIMESURVEY_SERVER2_PASSWORD=...
```
- [ ] Upravit config_loader.py pro načítání více serverů
- [ ] Přidat výběr serveru v CLI menu
- [ ] Aktualizovat limesurvey_client.py

#### Důvod:
Máte dva servery k dispozici a potřebujete s nimi pracovat.

### 3. AI textové zpracování + tabulkové grafy (1-2 týdny)
**Priorita: VYSOKÁ**

#### Co udělat:
- [ ] Implementovat AI kategorizaci textových odpovědí
- [ ] Možnost uložení kategorizace pro opakované použití
- [ ] Nový typ grafu - tabulka v Datawrapper
- [ ] Integrace do stávajícího workflow

#### Důvod:
Ruční kategorizace při opakovaném generování je neudržitelná.

## Kroky k odložení na později

### Odložit na Fázi 2 (Q2 2025):
- **Podpora více LimeSurvey serverů** - vyžaduje významné změny v architektuře
- **Rozšířená metadata** - potřebuje stabilní základ pro testování
- **AI funkce** - vyžadují dodatečné závislosti a komplexní testování

### Odložit na Fázi 3+ (Q3 2025 a později):
- **Nové typy grafů** - nejprve stabilizovat stávající
- **GUI rozšíření** - vyžaduje značné zdroje
- **Speciální analýzy** - specializované funkce pro pokročilé uživatele

## Konkrétní implementační kroky pro tento týden

### Den 1: Refaktoring datové struktury
1. Vytvořit nové adresáře `data/` a `charts/` na root level
2. Přesunout stávající data se zachováním struktury
3. Aktualizovat cesty v config_loader.py a dalších modulech
4. Otestovat na stávajících datech

### Den 2-3: Podpora více serverů
1. Rozšířit .env o konfiguraci druhého serveru
2. Upravit config_loader.py pro načítání více serverů
3. Přidat výběr serveru do CLI menu
4. Aktualizovat limesurvey_client.py pro práci s více servery

### Den 4-5: Příprava AI funkcí
1. Připravit skeleton pro AI textové zpracování
2. Navrhnout strukturu pro ukládání kategorizací
3. Připravit základ pro tabulkové grafy
4. Otestovat celý workflow s oběma servery

## Kritéria pro přechod k dalším fázím

### Před Fází 2:
- [ ] Všechny základní funkce fungují bez chyb
- [ ] Dokumentace je kompletní a aktuální
- [ ] Unit testy pokrývají alespoň 80% kódu
- [ ] Aplikace je testována na alespoň 5 různých průzkumech

### Před implementací AI funkcí:
- [ ] Stabilní API pro předávání dat do AI
- [ ] Bezpečnostní opatření pro citlivá data
- [ ] Testovací framework pro AI výstupy
- [ ] Uživatelské rozhraní pro správu promptů

## Doporučení pro harmonogram

### Leden 2025: Konsolidace
- Dokončit všechny okamžité kroky
- Připravit stabilní verzi 1.0

### Únor-Březen 2025: Plánování Fáze 2
- Detailní analýza požadavků na více serverů
- Návrh architektury pro metadata
- Prototypy nových funkcí

### Duben+ 2025: Implementace podle plánu
- Postupná implementace podle priorit
- Pravidelné testování a feedback

## Poznámky

### Rizika:
- Příliš rychlé přidávání funkcí může destabilizovat aplikaci
- AI funkce vyžadují pečlivé testování kvůli neprediktabilním výstupům
- GUI implementace je časově náročná

### Příležitosti:
- Modulární architektura umožní snadné přidávání funkcí
- AI funkce mohou výrazně zlepšit uživatelský zážitek
- Podpora více serverů rozšíří použitelnost

### Doporučení:
- Držet se konzervativního přístupu
- Každou novou funkci důkladně otestovat
- Pravidelně sbírat feedback od uživatelů
- Udržovat zpětnou kompatibilitu
