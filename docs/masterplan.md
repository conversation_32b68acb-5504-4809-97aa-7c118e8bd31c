# Rozšířený masterplan pro Python program propojující LimeSurvey a Datawrapper

Program bude mít dvě klíčové funkcionality:

1. stáhne pomocí API LimeSurvey data odpovědí na otázky a strukturu průzkumu.
2. Transformuje data a vytvoří grafy v příjemném uživatelskm rozhraní do Datawrapper na základě uživatelských preferencí.

**Poznámka:** Tento dokument obsahuje základní masterplan. Pro detailní plán rozšíření viz [06_plan_rozsireni.md](06_plan_rozsireni.md).

## Co program dělá a jak
Programové workflow - základní smyčka:

### Načtení CSV dat

Načíst CSV data z API LimeSurvey.
Dekódovat data do textového formátu.
Provedení kontroly integrity dat (validace formátu, konzistence hodnot apod.).
Oprava dat podle definovaných pravidel.
Uložení zpracovaných dat.
Parametry:
ID průzkumu.
Výběr: všechna data nebo pouze dokončená data.
Formát hlavičky: názvy nebo ID otázek.
Formát dat: názvy nebo ID odpovědí.
Načtení struktury průzkumu

Použití API volání get_fieldmap LimeSurvey pro získání struktury průzkumu.
Uložení struktury jako transformačního souboru ve formátu JSON.
Obsahuje názvy otázek vhodné pro generování grafů.
Slouží k propojení CSV dat s upravenými názvy a zjištění typů otázek.
Vytvoření mappingu názvů otázek a odpovědí z LSS (pro využití v Datawrapper).
Manuální úprava mappingu názvů otázek a odpovědí
(Volitelně v budoucí verzi: využití OpenAI pro automatickou opravu nebo návrhy změn v mappingu názvů otázek a odpovědí.)
Validace souborů

Zkontrolovat, zda jsou v adresáři s ID průzkumu dva validní soubory:
Upravený CSV soubor.
Upravený transformační JSON soubor.
Propojení CSV a JSON souborů

Načíst CSV soubor a transformační JSON soubor.
Sloučit data na základě ID nebo názvů z transformačního souboru.
Generovat finální názvy pro otázky a odpovědi v CSV.
Transformace CSV dat na long formát

Převedení CSV dat do long formátu (vhodné pro následné zpracování a vizualizaci).
Zachování vazeb mezi otázkami a odpověďmi.
Analýza otázek a generování datové struktury pro grafy

Zjistit hranice sloupců pro každou otázku a její typ (např. výběr z možností, škála, otevřená otázka).
Vytvořit datovou strukturu vhodnou pro vizualizaci každého typu otázky.
Doplnit data o metadata potřebná pro generování grafů (např. popisky os, jednotky, kategorizace odpovědí).
Generování grafů

Použití Datawrapper API:
Vytvoření grafu pro každý typ otázky.
Automatické přiřazení dat a struktury ke grafům.
Uložení odkazů nebo ID grafů pro další použití.
Uložení a export výsledků

Uložit výsledné datové struktury a grafy do adresáře průzkumu.
Exportovat grafy ve zvoleném formátu (např. obrázky, odkazy).
Dokumentace a logování

Generování logu o zpracování (kroky, chyby, upozornění).
Vytvoření souhrnu zpracovaných dat a generovaných grafů.

## Vytvoření grafů v Datawrapper

Vytvoří se složka pro průzkum (pojmenovaná podle ID průzkumu).
Pro každou otázku se vytvoří nejvhodnější graf se základním názvem a metadaty.
Volitelně se nahrají zpracovaná data a nastaví typ grafu.
Publikace grafů:
Program publikuje grafy (pokud je zvolena tato možnost).
Lze stáhnout PNG soubory.

Ovládání programu
Ovládání pomocí CLI
Program bude možné spouštět z příkazové řádky s následující syntaxí:

css
Zkopírovat kód
program [operace] [parametry]
Hlavní operace a parametry:
Načtení dat

css
Zkopírovat kód
program load_data --survey_id [ID] --completed [true/false] --header [name/id] --data_format [name/id]
--survey_id: ID průzkumu.
--completed: Zda se mají načítat dokončená data (true/false).
--header: Použít názvy (name) nebo ID (id) v hlavičce.
--data_format: Použít názvy (name) nebo ID (id) v datech.
Načtení struktury průzkumu

css
Zkopírovat kód
program get_structure --survey_id [ID]
--survey_id: ID průzkumu.
Propojení CSV a JSON souborů

css
Zkopírovat kód
program link_data --survey_id [ID]
--survey_id: ID průzkumu.
Transformace na long formát

css
Zkopírovat kód
program transform_data --survey_id [ID]
--survey_id: ID průzkumu.
Generování datové struktury pro grafy

css
Zkopírovat kód
program prepare_graphs --survey_id [ID]
--survey_id: ID průzkumu.
Generování grafů pomocí Datawrapper API

css
Zkopírovat kód
program generate_charts --survey_id [ID]
--survey_id: ID průzkumu.
Vypsání logu

css
Zkopírovat kód
program show_log --survey_id [ID]
--survey_id: ID průzkumu.
Ovládání pomocí číslovaného menu
Při spuštění programu bez parametrů se zobrazí číslované menu:

Hlavní menu:
Vypsání seznamu průzkumů (volá API a zobrazí dostupné průzkumy).
Uživatel vybere číslo průzkumu z nabídky nebo stiskne Enter pro návrat.
Načtení dat průzkumu:
Nabídka parametrů s defaultními hodnotami:
Zda načítat dokončená data (default: true).
Hlavička (default: name).
Data (default: name).
Stisk Enter potvrzuje defaultní hodnoty.
Načtení struktury průzkumu:
Volba Enter provede načtení.
Propojení CSV a JSON souborů:
Stisk Enter provede propojení.
Transformace CSV dat na long formát:
Stisk Enter spustí transformaci.
Generování datové struktury pro grafy:
Stisk Enter spustí přípravu.
Generování grafů:
Stisk Enter spustí generování grafů.
Zobrazení logu průzkumu:
Stisk Enter zobrazí log.
Ukázka číslovaného menu:
markdown
Zkopírovat kód
=== Hlavní menu ===
1. Vypsání seznamu průzkumů
2. Načtení dat průzkumu
3. Načtení struktury průzkumu
4. Propojení CSV a JSON souborů
5. Transformace CSV dat na long formát
6. Generování datové struktury pro grafy
7. Generování grafů
8. Zobrazení logu průzkumu
0. Ukončit program

Vyberte možnost (default: 0): 
Způsob ovládání
CLI: Efektivní pro zkušené uživatele, kteří znají parametry.
Číslované menu: Umožňuje postupné zpracování průzkumu pro méně zkušené uživatele.
Kombinace: Program může po spuštění bez parametrů přepnout do menu nebo provést výchozí operaci (např. výpis průzkumů).

1. Mapování a transformace názvů
Zkrácení a přejmenování názvů
Program bude používat následující přístup:



Titulek grafu: Generuje se zkrácený název otázky.
Popisek (description): Volitelný delší popis.
Patička: Automaticky přidá „Data z LimeSurvey".
Typ grafu: Výchozí je sloupcový graf, lze přizpůsobit podle typu otázky.
5. Prázdné grafy jako základní funkcionalita
Pokud je zvolen parametr --empty-graphs, program:

Načte strukturu průzkumu:
Stáhne názvy otázek a typy odpovědí z LSS.
Vytvoří grafy v Datawrapper:
Pro každou otázku se vytvoří prázdný graf.
Nastaví se základní metadata (název, popis, patička).
Grafy zůstanou prázdné: Data budou připravena k ručnímu nahrání.
6. Metadata a přizpůsobení v .env
Kromě parametrů z LimeSurvey a Datawrapper bude .env obsahovat metadata, která ovlivní grafy:


DEFAULT_CHART_TYPE=bar_chart  # Výchozí typ grafu
GRAPH_FOOTER="Data z LimeSurvey"  # Text v patičce grafu
EXPORT_PNG=true  # Automatické stahování PNG grafů

## Plánovaná struktura programu

project/
├── src/
│   ├── config_loader.py
│   ├── limesurvey_client.py
│   ├── data_transformer.py
│   ├── ai_name_mapper.py  # Modul pro mapování názvů (bude rozšířen v další fázi)
│   ├── datawrapper_client.py
│   ├── cli.py  # Hlavní rozhraní
│   └── main.py  # Spouštěcí soubor
├── .env
├── data_mapping.json  # Volitelné ruční mapování názvů
├── requirements.txt
└── README.md

### Hlavní moduly

config_loader.py → Načítání parametrů z .env.
limesurvey_client.py → Stažení dat a struktury z LimeSurvey.
data_transformer.py → Převod dat (long/aggregace, operace s textem).
ai_name_mapper.py → Mapování názvů (bude rozšířeno v další fázi).
datawrapper_client.py → Tvorba složek, grafů a jejich publikace.
cli.py → Spouštění programu přes příkazovou řádku.
menu.py → Menu pro výběr parametrů programu.


## GUI Framework

GUI je implementováno pomocí PyQt5. Hlavní okno je rozděleno na 2 hlavní sekce:

1. Levá část:
   - Seznam průzkumů
   - Seznam otázek vybraného průzkumu

2. Pravá část:
   - Záložky:
     1. Otázky - detail otázky, nastavení a generování grafů
     2. CSV Data - tabulka s daty z CSV, stránkování, třídění, filtrování
     3. Struktura - stromová struktura LSS souboru
     4. Transformace - tabulka mapování otázek, stránkování, třídění, filtrování
   - Log okno
   - CLI příkazový řádek

Menu:
- Soubor
  - Načíst průzkumy
  - Uložit nastavení
  - Konec

## Plán integrace OpenAI (následující fáze)

1. Textové zpracování
- Klíčová slova z textových odpovědí
- Shrnutí textových odpovědí  
- Kategorizace odpovědí

2. Mapování názvů
- Automatické generování stručných názvů otázek
- Tvorba popisků grafů
- Generování metadat

3. Prompt engineering
- Definice systémových promptů pro konzistentní výstupy
- Uživatelské prompty pro specifické úpravy textů
- Validace a filtrování výstupů

4. Implementace
- Nový modul openai_client.py
- Integrace s existujícími moduly
- Zpracování chyb a rate limitů
