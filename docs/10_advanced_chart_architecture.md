# Advanced Chart Architecture - Kompletní návrh

## 🎯 Přehled architektury

Pokročilá architektura pro generování grafů s AI analýzou, virtu<PERSON>lními otázkami a externími generátory.

### Klíčové komponenty:
1. **Chart Type Configuration System** - konfigurace typů grafů
2. **Virtual Question Manager** - sloučené/vypočítané otázky  
3. **AI Data Analyst** - automatická analýza dat
4. **Chart Generator Router** - směrování mezi generátory
5. **External Generators** - WordCloud, Table, Multi-Chart

## 🏗️ Architektonická změna

### SOUČASNÝ STAV:
```
LSS → CSV → chart_data.json → Datawrapper API → PNG
```

### NOVÝ STAV:
```
LSS → CSV → 
  ↓
📋 CHART TYPE CONFIGURATION
  ↓
chart_data.json (rozšířený) → 
  ↓
🤖 AI DATA ANALYST
  ↓
🔀 CHART GENERATOR ROUTER:
  ├── Datawrapper API → PNG
  ├── WordCloud Generator → PNG  
  ├── Table Generator → PNG
  └── Multi-Chart Generator → PNG
```

## 📊 Datové struktury

### Rozšířený chart_data.json:
```json
{
  "survey_info": {...},
  "questions": {
    "Q123": {
      "type": "original",
      "question_text": "Co se vám líbilo?",
      "data_type": "text",
      "hidden": false,
      "charts": ["wordcloud", "table"],
      "raw_data": [...],
      "processed_data": {
        "wordcloud": {
          "frequencies": {...},
          "ai_analysis": {...}
        }
      }
    },
    "VIRTUAL_satisfaction": {
      "type": "virtual",
      "question_text": "Celková spokojenost (sloučeno)",
      "source_questions": ["Q123", "Q124", "Q125"],
      "merge_strategy": "concatenate",
      "hidden": false,
      "charts": ["wordcloud", "summary_table"],
      "computed_data": {...},
      "ai_analysis": {
        "summary": "Respondenti jsou převážně spokojeni...",
        "key_insights": [...],
        "sentiment_score": 0.7,
        "metadata_text": "Analýza ukazuje vysokou spokojenost..."
      }
    }
  },
  "chart_configs": {
    "Q123_wordcloud": {
      "generator": "internal_wordcloud",
      "prompt_template": "general_keywords",
      "custom_prompt": "Extrahuj 50 klíčových slov...",
      "parameters": {"max_words": 50, "color_scheme": "blue"},
      "ai_analysis": {
        "enabled": true,
        "prompt_template": "graph_analysis_wordcloud",
        "output_location": "footer"
      }
    }
  }
}
```

### Chart Type Configuration:
```json
{
  "default_assignments": {
    "text": ["wordcloud", "table"],
    "likert": ["column", "donut"],
    "choice": ["column", "pie"],
    "numeric": ["histogram", "box"]
  },
  "question_overrides": {
    "Q123": {
      "charts": ["wordcloud"],
      "custom_settings": {...}
    }
  },
  "virtual_questions": {
    "VIRTUAL_satisfaction": {
      "name": "Celková spokojenost",
      "source_questions": ["Q123", "Q124"],
      "merge_strategy": "concatenate",
      "charts": ["wordcloud"]
    }
  }
}
```

## 🔧 Klíčové komponenty

### 1. Chart Type Configuration System
```python
class ChartTypeConfigManager:
    def __init__(self):
        self.config_file = "config/chart_types.json"
        self.default_assignments = self.load_defaults()
    
    def configure_question_charts(self, question_id, question_type):
        """Konfigurace grafů pro otázku"""
        suggested = self.suggest_chart_types(question_type)
        selected = self.user_select_charts(suggested)
        self.save_question_config(question_id, selected)
    
    def suggest_chart_types(self, data_type):
        """Návrh typů grafů podle typu dat"""
        return self.default_assignments.get(data_type, ["column"])
```

### 2. Virtual Question Manager
```python
class VirtualQuestionManager:
    def create_virtual_question(self, name, source_questions, strategy):
        """Vytvoří virtuální otázku"""
        virtual_q = {
            "type": "virtual",
            "question_text": name,
            "source_questions": source_questions,
            "merge_strategy": strategy,
            "hidden": False
        }
        return virtual_q
    
    def compute_virtual_data(self, virtual_question):
        """Vypočítá data pro virtuální otázku"""
        if virtual_question['merge_strategy'] == 'concatenate':
            return self.concatenate_text_data(virtual_question)
        elif virtual_question['merge_strategy'] == 'aggregate':
            return self.aggregate_numeric_data(virtual_question)
    
    def toggle_visibility(self, question_id):
        """Toggle hidden atribut - jako Excel hidden columns"""
        question = self.get_question(question_id)
        question['hidden'] = not question['hidden']
        
        if not question['hidden']:
            self.generate_charts_for_question(question_id)
```

### 3. AI Data Analyst
```python
class AIDataAnalyst:
    def __init__(self, ai_manager):
        self.ai_manager = ai_manager
        self.prompt_templates = self.load_analysis_prompts()
    
    def analyze_chart_data(self, chart_data, chart_type):
        """Analyzuje data a generuje závěry"""
        prompt_template = self.get_analysis_prompt(chart_type)
        
        analysis = self.ai_manager.analyze_text_with_ai(
            text=self.prepare_data_summary(chart_data),
            analysis_type='data_analysis',
            template=prompt_template
        )
        
        return {
            "summary": analysis['summary'],
            "key_insights": analysis['insights'],
            "metadata_text": analysis['footer_text'],
            "confidence": analysis['confidence']
        }
    
    def embed_analysis_in_chart(self, chart_config, analysis):
        """Vloží AI analýzu do chart metadat"""
        if analysis['output_location'] == 'footer':
            chart_config['annotate']['notes'] = analysis['metadata_text']
        elif analysis['output_location'] == 'header':
            chart_config['annotate']['intro'] = analysis['metadata_text']
```

### 4. Chart Generator Router
```python
class ChartGeneratorRouter:
    def __init__(self):
        self.generators = {
            'datawrapper': DatawrapperGenerator(),
            'internal_wordcloud': WordCloudGenerator(),
            'internal_table': TableGenerator(),
            'internal_multi': MultiChartGenerator()
        }
    
    def generate_chart(self, chart_config, data):
        """Směruje generování podle typu generátoru"""
        generator_type = chart_config['generator']
        generator = self.generators[generator_type]
        
        # AI analýza před generováním
        if chart_config.get('ai_analysis', {}).get('enabled'):
            analysis = self.ai_analyst.analyze_chart_data(data, chart_config['type'])
            self.embed_analysis(chart_config, analysis)
        
        return generator.create_chart(chart_config, data)
```

## 📋 User Flow Test Cases

### UF1: Merge Text Questions to WordCloud
```
Uživatel chce spojit dvě textové otázky a vytvořit jeden WordCloud
- Vstup: Q123 + Q124 (textové otázky)
- Akce: Vytvoří virtuální otázku "VIRTUAL_combined"
- Konfigurace: Pouze podstatná jména, defaultní barvy
- Výstup: WordCloud s AI analýzou v patičce
```

### UF2: Multi Donuts from 6 Questions  
```
Uživatel vybere 6 otázek pro multi donuts graf 3x2
- Vstup: Q101-Q106 (likert škály)
- Akce: Konfigurace multi-chart generátoru
- Parametry: 3x2 layout, Datawrapper atributy
- Výstup: Multi donuts graf přes Datawrapper API
```

### UF3: Named and Translated WordCloud
```
WordCloud ze 3 otázek s vlastním názvem a překladem
- Vstup: Q123, Q124, Q125
- Akce: Vytvoří pojmenovanou virtuální otázku
- Překlad: Název se přidá do translation.json
- Výstup: WordCloud s překladem do EN
```

### UF4: Persistent Settings
```
Opakované generování bez nutnosti znovu nastavovat
- Problém: Uživatel nechce stále nastavovat WordCloud parametry
- Řešení: Uložení konfigurace do chart_types.json
- Výsledek: Parametry se pamatují mezi spuštěními
```

## 🚀 Implementační plán

### Fáze 1: Foundation (1-2 týdny)
1. Chart Type Configuration System
2. Chart Data JSON rozšíření  
3. Chart Generator Router základy
4. Virtual Question Manager

### Fáze 2: AI Integration (1 týden)
1. AI Data Analyst Core
2. Analysis Prompt Templates
3. Chart Metadata Integration

### Fáze 3: External Generators (1-2 týdny)
1. WordCloud Generator Integration
2. Table Generator
3. Multi-Chart Generator

### Fáze 4: Menu Implementation (1 týden)
1. Menu 10: AI WordCloud
2. Menu 11: Chart Type Configuration
3. Menu 12: Virtual Questions Management
4. Menu 13: AI Graph Analysis
5. Menu 14: Chart Settings Reset

## 📁 Soubory k vytvoření

```
src/core/
├── chart_config_manager.py      # Chart type configuration
├── virtual_questions.py         # Virtual question management
└── chart_router.py             # Chart generation routing

src/ai/
├── data_analyst.py             # AI data analysis
└── analysis_prompts.yaml       # Analysis prompt templates

src/generators/
├── __init__.py
├── base_generator.py           # Base class for generators
├── wordcloud_generator.py      # WordCloud external generator
├── table_generator.py          # Table generator
└── multi_chart_generator.py    # Multi-chart generator

config/
├── chart_types.json           # Chart type configurations
├── analysis_prompts.yaml      # AI analysis prompts
└── generator_settings.json    # Generator-specific settings
```

## 🎯 Success Criteria

### Minimální viable product:
- [ ] Chart Type Configuration funguje
- [ ] Virtuální otázky lze vytvářet a spravovat
- [ ] WordCloud generator funguje jako externí
- [ ] AI analýza se vkládá do metadat
- [ ] Všechny user flow testy procházejí

### Plná implementace:
- [ ] Všechny generátory implementovány
- [ ] Kompletní menu systém
- [ ] Backward compatibility zachována
- [ ] Performance optimalizace
- [ ] Kompletní dokumentace

**Dobrou noc! Zítra máme jasný plán implementace! 🌙**
