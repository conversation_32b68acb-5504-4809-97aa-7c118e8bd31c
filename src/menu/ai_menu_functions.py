"""
AI Menu Functions
Nové AI menu funkce - BEZPEČNÉ, neovlivňují stávající menu
"""

import logging
import os
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


def check_ai_availability() -> bool:
    """
    Kontrola dostupnosti AI funkcí
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    try:
        from ..ai.integration import get_ai_integration, is_ai_available
        return is_ai_available()
    except ImportError:
        return False
    except Exception as e:
        logger.error(f"AI availability check failed: {e}")
        return False


def display_ai_status():
    """Zobrazí stav AI funkcí"""
    print("\n=== AI Funkce - Stav ===")
    
    if not check_ai_availability():
        print("❌ AI funkce nejsou dostupné")
        print("\n💡 Pro aktivaci AI funkcí:")
        print("   1. Nainstalujte závislosti: pip install -r requirements_ai.txt")
        print("   2. Nastavte OPENAI_API_KEY v .env souboru")
        print("   3. Restartujte aplikaci")
        return False
    
    try:
        from ..ai.integration import get_ai_integration
        ai = get_ai_integration()
        
        if ai:
            stats = ai.get_usage_statistics()
            print("✅ AI funkce jsou dostupné")
            print(f"📊 Celkové požadavky: {stats.get('openai', {}).get('total_requests', 0)}")
            print(f"💰 Celkové náklady: ${stats.get('openai', {}).get('total_cost', 0):.4f}")
            print(f"🎯 Cache hit rate: {stats.get('openai', {}).get('cache_hit_rate', 0)}%")
            return True
        else:
            print("⚠️ AI integrace není inicializována")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při kontrole AI stavu: {e}")
        return False


def menu_ai_wordcloud():
    """
    Menu 10: AI WordCloud generování
    BEZPEČNÉ - nová funkce, neovlivňuje stávající menu
    """
    print("\n" + "="*50)
    print("         Menu 10: AI WordCloud Generování")
    print("="*50)
    
    # Kontrola dostupnosti AI
    if not check_ai_availability():
        display_ai_status()
        input("\nStiskněte Enter pro návrat do hlavního menu...")
        return
    
    try:
        from ..ai.integration import get_ai_integration
        from ..core.chart_config_manager import create_chart_config_manager
        from ..core.enhanced_chart_data import create_enhanced_chart_data_manager
        from ..generators.wordcloud_chart_generator import create_wordcloud_generator
        
        ai = get_ai_integration()
        if not ai:
            print("❌ AI integrace není dostupná")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Zobrazit aktuální stav
        display_ai_status()
        
        print("\n📋 WordCloud Generování - Kroky:")
        print("1. Výběr survey a textových otázek")
        print("2. Konfigurace AI zpracování")
        print("3. Nastavení vizualizace")
        print("4. Generování a uložení")
        
        # Krok 1: Výběr survey
        print("\n" + "-"*30)
        print("Krok 1: Výběr Survey")
        print("-"*30)
        
        survey_id = input("Zadejte Survey ID: ").strip()
        if not survey_id:
            print("❌ Survey ID je povinné")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Krok 2: Simulace výběru textových otázek (placeholder)
        print(f"\n📊 Survey: {survey_id}")
        print("🔍 Hledám textové otázky...")
        
        # TODO: Napojit na skutečné načítání dat
        print("⚠️ Placeholder: Simuluji textové otázky")
        sample_questions = {
            "Q1": "Co se vám líbilo na naší službě?",
            "Q2": "Jaké máte návrhy na zlepšení?",
            "Q3": "Další komentáře"
        }
        
        print("\nNalezené textové otázky:")
        for q_id, q_text in sample_questions.items():
            print(f"  {q_id}: {q_text}")
        
        # Výběr otázek
        print("\nVyberte otázky (oddělte čárkou, např. Q1,Q2):")
        selected_input = input("Otázky: ").strip()
        
        if not selected_input:
            selected_questions = list(sample_questions.keys())
            print(f"✅ Použiji všechny otázky: {', '.join(selected_questions)}")
        else:
            selected_questions = [q.strip() for q in selected_input.split(',')]
            print(f"✅ Vybrané otázky: {', '.join(selected_questions)}")
        
        # Krok 3: Konfigurace AI
        print("\n" + "-"*30)
        print("Krok 2: Konfigurace AI")
        print("-"*30)
        
        use_ai = input("Použít AI enhancement? (y/n) [y]: ").strip().lower()
        use_ai = use_ai != 'n'
        
        if use_ai:
            print("🤖 AI enhancement zapnut")
            print("📝 Dostupné prompt templates:")
            print("  1. general_keywords - Obecná klíčová slova")
            print("  2. sentiment_words - Slova podle sentimentu")
            print("  3. categories - Kategorizace odpovědí")
            
            prompt_choice = input("Vyberte template (1-3) [1]: ").strip()
            prompt_templates = {
                '1': 'general_keywords',
                '2': 'sentiment_words', 
                '3': 'categories'
            }
            selected_prompt = prompt_templates.get(prompt_choice, 'general_keywords')
            print(f"✅ Vybrán prompt: {selected_prompt}")
        else:
            print("📊 Použije se základní zpracování")
            selected_prompt = None
        
        # Krok 4: Konfigurace vizualizace
        print("\n" + "-"*30)
        print("Krok 3: Konfigurace Vizualizace")
        print("-"*30)
        
        print("🎨 Dostupné šablony:")
        print("  1. presentation - Pro prezentace (1200x600)")
        print("  2. web - Pro web (800x400)")
        print("  3. print - Pro tisk (2400x1200)")
        print("  4. custom - Vlastní nastavení")
        
        template_choice = input("Vyberte šablonu (1-4) [2]: ").strip()
        templates = {
            '1': 'presentation',
            '2': 'web',
            '3': 'print',
            '4': 'custom'
        }
        selected_template = templates.get(template_choice, 'web')
        
        # Vlastní nastavení
        if selected_template == 'custom':
            try:
                width = int(input("Šířka [800]: ") or "800")
                height = int(input("Výška [400]: ") or "400")
                max_words = int(input("Max. počet slov [200]: ") or "200")
                
                custom_config = {
                    'width': width,
                    'height': height,
                    'max_words': max_words
                }
                print(f"✅ Vlastní konfigurace: {custom_config}")
            except ValueError:
                print("⚠️ Neplatné hodnoty, použiji výchozí")
                selected_template = 'web'
                custom_config = {}
        else:
            custom_config = {}
            print(f"✅ Vybrána šablona: {selected_template}")
        
        # Krok 5: Generování
        print("\n" + "-"*30)
        print("Krok 4: Generování WordCloud")
        print("-"*30)
        
        print("🚀 Spouštím generování...")
        
        # Simulace dat (placeholder)
        sample_text_data = {
            'Q1': ['kvalita služeb', 'rychlé vyřízení', 'příjemný personál', 'dobré ceny'],
            'Q2': ['více možností', 'lepší komunikace', 'rychlejší odpovědi'],
            'Q3': ['celkově spokojen', 'doporučuji ostatním', 'skvělá zkušenost']
        }
        
        # Kombinace textů z vybraných otázek
        combined_texts = []
        for q_id in selected_questions:
            if q_id in sample_text_data:
                combined_texts.extend(sample_text_data[q_id])
        
        combined_text = ' '.join(combined_texts)
        print(f"📝 Zpracovávám {len(combined_texts)} textových odpovědí...")
        
        # Generování WordCloud
        try:
            if use_ai:
                print("🤖 Používám AI enhancement...")
                result = ai.generate_wordcloud_for_survey(
                    survey_id=survey_id,
                    question_ids=selected_questions,
                    use_ai=True
                )
            else:
                print("📊 Používám základní zpracování...")
                # Fallback na základní generování
                result = _generate_basic_wordcloud_fallback(
                    combined_text, 
                    survey_id, 
                    selected_template,
                    custom_config
                )
            
            if result.get('success'):
                print("✅ WordCloud úspěšně vygenerován!")
                print(f"📁 Uloženo do: charts/{survey_id}/wordclouds/")
                
                if 'frequencies' in result:
                    top_words = list(result['frequencies'].items())[:5]
                    print(f"🔝 Top slova: {', '.join([f'{word}({count})' for word, count in top_words])}")
                
                if 'ai_analysis' in result:
                    analysis = result['ai_analysis']
                    if 'summary' in analysis:
                        print(f"🧠 AI analýza: {analysis['summary']}")
                
            else:
                print(f"❌ Chyba při generování: {result.get('error', 'Neznámá chyba')}")
                
        except Exception as e:
            print(f"❌ Chyba při generování WordCloud: {e}")
            logger.error(f"WordCloud generation error: {e}")
        
        # Zobrazit statistiky
        print("\n" + "-"*30)
        print("Statistiky")
        print("-"*30)
        
        try:
            stats = ai.get_usage_statistics()
            openai_stats = stats.get('openai', {})
            print(f"💰 Náklady této operace: ~$0.01-0.05")
            print(f"📊 Celkové náklady: ${openai_stats.get('total_cost', 0):.4f}")
            print(f"🎯 Cache hit rate: {openai_stats.get('cache_hit_rate', 0)}%")
        except:
            print("📊 Statistiky nejsou dostupné")
        
    except ImportError as e:
        print(f"❌ Chybí AI moduly: {e}")
        print("💡 Spusťte: pip install -r requirements_ai.txt")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {e}")
        logger.error(f"Menu AI WordCloud error: {e}")
    
    input("\nStiskněte Enter pro návrat do hlavního menu...")


def _generate_basic_wordcloud_fallback(
    text: str, 
    survey_id: str, 
    template: str,
    custom_config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Fallback generování WordCloud bez AI
    BEZPEČNÉ - základní funkcionalita
    """
    try:
        from wordcloud import WordCloud
        import matplotlib.pyplot as plt
        from pathlib import Path
        
        # Konfigurace podle šablony
        configs = {
            'presentation': {'width': 1200, 'height': 600, 'max_words': 150},
            'web': {'width': 800, 'height': 400, 'max_words': 200},
            'print': {'width': 2400, 'height': 1200, 'max_words': 300}
        }
        
        config = configs.get(template, configs['web'])
        config.update(custom_config)
        
        # Vytvoření WordCloud
        wordcloud = WordCloud(
            width=config['width'],
            height=config['height'],
            max_words=config['max_words'],
            background_color='white',
            random_state=42
        ).generate(text)
        
        # Uložení
        output_dir = Path(f"charts/{survey_id}/wordclouds")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / "wordcloud_basic.png"
        wordcloud.to_file(str(output_path))
        
        return {
            'success': True,
            'output_path': str(output_path),
            'frequencies': dict(wordcloud.words_),
            'ai_enhanced': False
        }
        
    except ImportError:
        return {
            'success': False,
            'error': 'WordCloud library not installed. Run: pip install wordcloud'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }
